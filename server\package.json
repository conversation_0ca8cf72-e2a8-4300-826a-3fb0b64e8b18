{"name": "auth-server", "version": "1.0.0", "description": "Authentication server with Node.js, Express, and MongoDB", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"@google/generative-ai": "^0.24.1", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "openai": "^5.9.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["auth", "node", "express", "mongodb"], "author": "", "license": "ISC"}