const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const dotenv = require('dotenv');
const authRoutes = require('./routes/auth');
const converterRoutes = require('./routes/converter');

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
// app.use(cors({
//   origin: ['http://localhost:5173', 'http://localhost:5174'], // Your frontend URLs
//   credentials: true // Allow cookies to be sent
// }));

const corsOptions = {
  origin: 'http://localhost:5173', // frontend origin
  credentials: true,               // allow credentials (cookies, etc.)
};

app.use(cors(corsOptions));

app.use(express.json());
app.use(cookieParser());

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/mern-auth', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('MongoDB connected successfully'))
.catch((err) => console.log('MongoDB connection error:', err));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/converter', converterRoutes);

app.get('/', (req, res) => {
  res.json({ message: 'Auth API is running!' });
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
