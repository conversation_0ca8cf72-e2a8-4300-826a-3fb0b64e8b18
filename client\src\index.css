@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white m-0 min-h-screen transition-colors;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 border border-gray-100 dark:border-gray-700;
  }
  
  .alert-error {
    @apply bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg;
  }
  
  .alert-success {
    @apply bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg;
  }

  .label-text {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
  }

  /* Monaco Editor Theme Styles */
  .monaco-editor {
    @apply rounded-lg !important;
  }

  /* Custom scrollbar for dark mode */
  .dark ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .dark ::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-gray-600 rounded;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}
